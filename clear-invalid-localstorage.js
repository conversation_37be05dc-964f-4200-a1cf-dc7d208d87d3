// <PERSON>ript to clear invalid localStorage data that might cause Angular table errors
// Run this in the browser console to clear problematic localStorage entries

console.log('Clearing invalid localStorage entries...');

// Clear requests table columns that might contain invalid column references
const requestsColumns = localStorage.getItem('requestsTableDisplayedColumns');
if (requestsColumns) {
  console.log('Found requestsTableDisplayedColumns:', requestsColumns);
  try {
    const parsed = JSON.parse(requestsColumns);
    const validColumns = ['select', 'id', 'userName', 'projectName', 'date', 'hours', 'description', 'process', 'shift', 'isOvertime', 'status', 'comments', 'actions'];
    const filteredColumns = parsed.filter(col => validColumns.includes(col));
    
    if (filteredColumns.length !== parsed.length) {
      console.log('Removing invalid columns:', parsed.filter(col => !validColumns.includes(col)));
      localStorage.setItem('requestsTableDisplayedColumns', JSON.stringify(filteredColumns));
      console.log('Updated requestsTableDisplayedColumns:', JSON.stringify(filteredColumns));
    } else {
      console.log('No invalid columns found in requestsTableDisplayedColumns');
    }
  } catch (error) {
    console.error('Error parsing requestsTableDisplayedColumns, removing:', error);
    localStorage.removeItem('requestsTableDisplayedColumns');
  }
}

// Clear time entry table columns that might contain invalid column references
const timeEntryColumns = localStorage.getItem('timeEntryTableDisplayedColumns');
if (timeEntryColumns) {
  console.log('Found timeEntryTableDisplayedColumns:', timeEntryColumns);
  try {
    const parsed = JSON.parse(timeEntryColumns);
    const validColumns = ['date', 'ldap', 'leadUsername', 'projectName', 'process', 'activity', 'timeInMins', 'attendanceType', 'isOvertime', 'comment', 'status', 'actions'];
    const filteredColumns = parsed.filter(col => validColumns.includes(col));
    
    if (filteredColumns.length !== parsed.length) {
      console.log('Removing invalid columns:', parsed.filter(col => !validColumns.includes(col)));
      localStorage.setItem('timeEntryTableDisplayedColumns', JSON.stringify(filteredColumns));
      console.log('Updated timeEntryTableDisplayedColumns:', JSON.stringify(filteredColumns));
    } else {
      console.log('No invalid columns found in timeEntryTableDisplayedColumns');
    }
  } catch (error) {
    console.error('Error parsing timeEntryTableDisplayedColumns, removing:', error);
    localStorage.removeItem('timeEntryTableDisplayedColumns');
  }
}

console.log('localStorage cleanup completed. Please refresh the page.');
