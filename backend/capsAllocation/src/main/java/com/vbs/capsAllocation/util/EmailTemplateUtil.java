package com.vbs.capsAllocation.util;

import com.vbs.capsAllocation.dto.VunnoMgmtDto;
import com.vbs.capsAllocation.dto.VunnoRequestDto;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class EmailTemplateUtil {

    public String getVunnoApprovalEmail(String backupInfo, String type, String fromDate, String toDate, String duration) {
        String backupDisplay = "Work from Home".equalsIgnoreCase(type) ? "N/A (WFH)" : backupInfo;
        String message = "Work from Home".equalsIgnoreCase(type) ? "" : "Please coordinate with your backup in your absence.";

        return "<html><body>"
                + "<h3>Your " + type + " request has been approved!</h3>"
                + "<p><b>Duration:</b> " + duration + "</p>"
                + "<p><b>From:</b> " + fromDate + " <b>To:</b> " + toDate + "</p>"
                + "<p><b>Backup:</b> " + backupDisplay + "</p>"
                + "<hr><p>" + message + "</p>"
                + "<p><b>PLEASE IGNORE THIS MAIL</b></p>"
                + "</body></html>";
    }



    public String getVunnoNotificationEmail(VunnoRequestDto requestDto, VunnoMgmtDto dto, List<Double> counts) {

        String applicationType = requestDto.getApplicationType();
        boolean isLeave = "Leave".equalsIgnoreCase(applicationType);
        boolean isDuration = "Multiple Days".equalsIgnoreCase(requestDto.getLvWfhDuration());

        String requestReason = isLeave ? requestDto.getBackupInfo() : requestDto.getReason();
        String requestFor = isLeave ? requestDto.getLeaveType() : "NA (WFH)";
        String extraSectionTitle = isLeave ? "Backup Information" : "Comment";
        String timesheetLink = isLeave ? requestDto.getTimesheetProof() : "NA";
        String oooLink = isLeave ? requestDto.getOooProof() : "NA";
        String duration = isDuration ? "Multiple Days" : requestDto.getLvWfhDuration();

        // Balances from counts
        double slBalance = counts.get(0);
        double clBalance = counts.get(1);
        double elBalance = counts.get(2);
        double totalBalance = counts.get(3);
        double totalWFHQuaterly = counts.get(5);
        double totalWFH = counts.get(4);
        double totalLeavesTakenThisQuarter = counts.size() > 4 ? counts.get(6) : 0.0;

        return "<!DOCTYPE html>"
                + "<html><head><base target=\"_top\"></head><body>"

                + "<h3>" + applicationType + " Request Notification</h3>"

                + "<p><b>Program Aligned:</b> " + dto.getProgramAlignment() + "</p>"

                + "<p>You have received a new request with the following details:</p>"

                + "<ul>"
                + "<li><b>Requester:</b> " + requestDto.getLdap() + "</li>"
                + "<li><b>Leave Duration:</b> " + requestDto.getStartDate() + " - " + requestDto.getEndDate() + "</li>"
                + "<li><b>Request Type:</b> " + requestFor + "</li>"
                + "<li><b>Request Duration:</b> " + duration + "</li>"
                + "<li><b>" + extraSectionTitle + ":</b> " + requestReason + "</li>"
                + "<li><b>Org Timesheet SS:</b> <a href='" + timesheetLink + "'>" + timesheetLink + "</a></li>"
                + "<li><b>Calendar OOO SS:</b> <a href='" + oooLink + "'>" + oooLink + "</a></li>"
                + "</ul>"

                + "<h4>Leave Balances and WFH</h4>"
                + "<ul>"
                + "<li><b>Sick Leave (SL):</b> " + slBalance + "</li>"
                + "<li><b>Casual Leave (CL):</b> " + clBalance + "</li>"
                + "<li><b>Earned Leave (EL):</b> " + elBalance + "</li>"
                + "<li><b>Total Leave Balance:</b> " + totalBalance + "</li>"
                + "<li><b>Total Leave Taken in Current Quarter:</b> " + totalLeavesTakenThisQuarter + "</li>"
                + "<li><b>Total WFH Taken in Current Quarter:</b> " + totalWFHQuaterly + "</li>"
                + "<li><b>Overall WFH:</b> " + totalWFH + "</li>"
                + "</ul>"

                + "<p><b>NOTE:</b> Please approve or deny this request via Teamsphere.</p>"
                + "<p><b>WFH Requests</b> can only be approved by manager.</p>"
                + "<p><b>Leave Requests</b> can be approved by any leads involved in this mail.</p>"
                + "<p><b>PLEASE IGNORE THIS MAIL</b></p>"


                + "</body></html>";
    }


}
