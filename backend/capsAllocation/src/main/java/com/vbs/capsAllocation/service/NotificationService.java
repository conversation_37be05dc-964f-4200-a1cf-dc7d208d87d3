package com.vbs.capsAllocation.service;

import com.vbs.capsAllocation.dto.VunnoMgmtDto;
import com.vbs.capsAllocation.dto.VunnoRequestDto;
import com.vbs.capsAllocation.model.Employee;
import com.vbs.capsAllocation.model.VunnoResponse;
import com.vbs.capsAllocation.repository.EmployeeRepository;
import com.vbs.capsAllocation.repository.LeaveUsageLogRepository;
import com.vbs.capsAllocation.util.EmailTemplateUtil;
import jakarta.mail.MessagingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


@Service
public class NotificationService {

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailTemplateUtil emailTemplateUtil;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private LeaveUsageLogRepository leaveUsageLogRepository;

    public void triggerRequestNotification(VunnoRequestDto requestDto, VunnoMgmtDto dto, List<Double> counts) {
        try {
            String ldap = requestDto.getLdap();
            String role = requestDto.getRole(); // USER, LEAD, MANAGER from frontend
            String managerLdap = dto.getManager(); // Program manager or reporting manager
            String DOMAIN = "@google.com";

            System.out.println("ROLE AND MANAGER: " + role + "  " + managerLdap);

            // Generate email body
            String emailBody = emailTemplateUtil.getVunnoNotificationEmail(requestDto, dto, counts);

            // Build recipient lists
            Set<String> ccList = new HashSet<>();
            String to = managerLdap + DOMAIN;

            if ("USER".equalsIgnoreCase(role)) {
                // TO: manager
                // CC: requestor + all leads under the same manager
                ccList.add(ldap + DOMAIN); // add requestor

                List<Employee> underManager = employeeRepository.findByProgramManager(managerLdap);
                for (Employee emp : underManager) {
                    if ("Team Lead".equalsIgnoreCase(emp.getLevel()) && emp.getLdap() != null) {
                        ccList.add(emp.getLdap() + DOMAIN);
                    }
                }
            } else if ("LEAD".equalsIgnoreCase(role) || "MANAGER".equalsIgnoreCase(role)) {
                // TO: manager
                // CC: only the requestor
                ccList.add(ldap + DOMAIN); // add requestor only
            }

            // Debug CC list
            for (String cc : ccList) {
                System.out.println("List of People in CC: " + cc);
            }

            // Email subject
            String subject = String.format("Teamsphere Testing Mail %s Request | %s | %s - %s | %s",
                    requestDto.getApplicationType(),
                    ldap,
                    requestDto.getStartDate(),
                    requestDto.getEndDate(),
                    requestDto.getLvWfhDuration()
            );

            List<String> testingList = List.of("<EMAIL>","<EMAIL>");
            // Send email ccList
            emailService.sendEmail(
                    List.of(to),
                    new ArrayList<>(ccList),
                    subject,
                    emailBody
            );

        } catch (Exception e) {
            System.err.println("Error sending approval notification email: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send approval notification email", e);
        }
    }

    public void triggerApprovalNotification(VunnoRequestDto requestDto, VunnoResponse response, String role, VunnoMgmtDto dto) {
        try {
            String DOMAIN = "@google.com";
            String ldap = requestDto.getLdap();           // Requestor
            String approverLdap = response.getApprover(); // Approver
            String managerLdap = dto.getManager();
            System.out.println("ROLE AND MANAGER: " + role + "  " + managerLdap);

            // Build recipient lists
            Set<String> ccList = new HashSet<>();
            String to = managerLdap + DOMAIN; // Final TO address - vf-gur-vbs

            if ("USER".equalsIgnoreCase(role)) {
                // TO: vf-gur-vbs
                // CC: requestor + all leads under the same manager
                ccList.add(ldap + DOMAIN);

                List<Employee> underManager = employeeRepository.findByProgramManager(managerLdap);
                for (Employee emp : underManager) {
                    if ("Team Lead".equalsIgnoreCase(emp.getLevel()) && emp.getLdap() != null) {
                        ccList.add(emp.getLdap() + DOMAIN);
                    }
                }
            } else if ("LEAD".equalsIgnoreCase(role) || "MANAGER".equalsIgnoreCase(role)) {
                // TO: vf-gur-vbs
                // CC: requestor + manager only
                ccList.add(ldap + DOMAIN);
                if (managerLdap != null && !managerLdap.equalsIgnoreCase(ldap)) {
                    ccList.add(managerLdap + DOMAIN);
                }
            }

            // Email subject
            String subject = String.format("Teamsphere Testing Mail Request Approved | %s | %s | %s - %s",
                    ldap,
                    response.getApplicationType(),
                    response.getFromDate(),
                    response.getToDate()
            );

            System.out.println("Request " + requestDto.getApplicationType() + " Response " + response.getApplicationType());

            // Email body
            String emailBody = emailTemplateUtil.getVunnoApprovalEmail(
                    response.getBackup(),
                    response.getApplicationType(),
                    response.getFromDate().toString(),
                    response.getToDate().toString(),
                    response.getDuration()
            );

            // Debug logging
            System.out.println("Sending approval mail TO: " + to);
            for (String cc : ccList) {
                System.out.println("CC: " + cc);
            }
            List<String> testingList = List.of("<EMAIL>","<EMAIL>");
            // Send email ccList
//            emailService.sendEmail(
//                    List.of(to),
//                    new ArrayList<>(testingList),
//                    subject,
//                    emailBody
//            );

        } catch (Exception e) {
            System.err.println("Error sending approval notification email: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send approval notification email", e);
        }
    }

}
